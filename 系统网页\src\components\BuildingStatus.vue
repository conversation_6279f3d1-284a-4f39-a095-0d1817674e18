<script setup>
import { computed, watch } from 'vue';

// 接收props
const props = defineProps({
  // 从App.vue传递的建筑状态
  status: {
    type: String,
    default: '正常状态'
  },
  statusType: {
    type: String,
    default: 'green'
  },
  description: {
    type: String,
    default: '系统运行正常'
  },
  // 新增：报警状态
  alarmState: {
    type: Object,
    default: () => ({
      alert: false,
      alertType: '',
      reporter: '',
      lastTriggered: null
    })
  }
});

// 计算属性：根据报警状态动态显示
const displayStatus = computed(() => {
  if (props.alarmState.alert) {
    return {
      status: '紧急状态',
      statusType: 'red',
      description: '手动报警器已触发'
    };
  } else {
    return {
      status: '正常状态',
      statusType: 'green',
      description: '系统运行正常'
    };
  }
});

// 监听报警状态变化
watch(() => props.alarmState.alert, (newAlert) => {
  console.log(`建筑状态更新: ${newAlert ? '报警' : '正常'}`);
});
</script>

<template>
  <div class="panel-section building-status">
    <div class="status-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      建筑状态
    </div>
    <div class="status-content">
      <div class="status-circle">
        <div class="circle" :class="displayStatus.statusType">
          {{ displayStatus.status }}
        </div>
      </div>
      <div class="status-text">{{ displayStatus.description }}</div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.status-title {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

/* 建筑状态样式 */
.status-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-circle {
  margin-bottom: 15px;
}

.circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: white;
}

.circle.red {
  background-color: #ff4d4f;
}

.circle.yellow {
  background-color: #faad14;
}

.circle.green {
  background-color: #52c41a;
}

.status-text {
  font-size: 0.9rem;
  color: #8ebbff;
}
</style>
