<script setup>
import { ref, computed } from 'vue';
import AlarmTypeSelector from './AlarmTypeSelector.vue';

// 导入引导员图片
import guideImage from '../assets/引导员图片.png';

// 引导员数据
const guideData = ref({
  id: 'G001',
  name: '李师傅',
  position: '地铁保安',
  location: 'B1层A区',
  phone: '***********',
  status: 'online', // online, offline, busy, emergency
  lastSeen: new Date().toLocaleString(),
  currentTask: null
});

// 引导员列表
const guideList = ref([
  { 
    id: 'G001', 
    name: '李师傅', 
    position: '地铁保安', 
    location: 'B1层A区', 
    phone: '***********', 
    status: 'online' 
  },
  { 
    id: 'G002', 
    name: '王师傅', 
    position: '安全员', 
    location: 'B2层B区', 
    phone: '***********', 
    status: 'busy' 
  },
  { 
    id: 'G003', 
    name: '张师傅', 
    position: '疏散员', 
    location: 'M层C区', 
    phone: '***********', 
    status: 'offline' 
  }
]);

// 当前选中的引导员
const selectedGuideId = ref('G001');

// 任务类型
const taskTypes = ref([
  { value: 'patrol', label: '巡逻检查' },
  { value: 'guide', label: '人员引导' },
  { value: 'emergency', label: '紧急疏散' },
  { value: 'maintenance', label: '设备维护' }
]);

// 派单表单
const taskForm = ref({
  type: 'patrol',
  description: '',
  priority: 'normal', // low, normal, high, urgent
  location: '',
  deadline: ''
});

// 显示派单表单
const showTaskForm = ref(false);

// 计算属性：状态颜色
const statusColor = computed(() => {
  switch (guideData.value.status) {
    case 'online': return '#52c41a';
    case 'offline': return '#8c8c8c';
    case 'busy': return '#faad14';
    case 'emergency': return '#ff4d4f';
    default: return '#8c8c8c';
  }
});

// 计算属性：状态文本
const statusText = computed(() => {
  switch (guideData.value.status) {
    case 'online': return '在线';
    case 'offline': return '离线';
    case 'busy': return '忙碌';
    case 'emergency': return '紧急';
    default: return '未知';
  }
});

// 选择引导员
const selectGuide = (guideId) => {
  selectedGuideId.value = guideId;
  const selectedGuide = guideList.value.find(guide => guide.id === guideId);
  if (selectedGuide) {
    guideData.value = { ...selectedGuide, lastSeen: new Date().toLocaleString() };
    emit('guide-selected', selectedGuide);
  }
};

// 显示派单表单
const showAssignTask = () => {
  if (guideData.value.status === 'offline') {
    console.log('引导员离线，无法派单');
    return;
  }
  showTaskForm.value = true;
  taskForm.value = {
    type: 'patrol',
    description: '',
    priority: 'normal',
    location: guideData.value.location,
    deadline: ''
  };
};

// 派发任务
const assignTask = () => {
  const task = {
    id: Date.now().toString(),
    guideId: guideData.value.id,
    guideName: guideData.value.name,
    ...taskForm.value,
    assignedAt: new Date().toLocaleString(),
    status: 'assigned'
  };
  
  guideData.value.currentTask = task;
  guideData.value.status = 'busy';
  
  emit('task-assigned', task);
  
  showTaskForm.value = false;
  console.log(`任务已派发给${guideData.value.name}`);
};

// 取消派单
const cancelAssignTask = () => {
  showTaskForm.value = false;
  taskForm.value = {
    type: 'patrol',
    description: '',
    priority: 'normal',
    location: '',
    deadline: ''
  };
};

// 语音通话
const startVoiceCall = () => {
  if (guideData.value.status === 'offline') {
    console.log('引导员离线，无法通话');
    return;
  }
  
  emit('voice-call-started', {
    guideId: guideData.value.id,
    guideName: guideData.value.name,
    phone: guideData.value.phone
  });
  
  console.log(`正在呼叫${guideData.value.name} (${guideData.value.phone})`);
};

// 发送消息
const sendMessage = (message) => {
  if (guideData.value.status === 'offline') {
    console.log('引导员离线，无法发送消息');
    return;
  }
  
  emit('guide-message-sent', {
    guideId: guideData.value.id,
    guideName: guideData.value.name,
    message: message,
    timestamp: new Date().toLocaleString()
  });
  
  console.log(`消息已发送给${guideData.value.name}: ${message}`);
};

// 更新位置
const updateLocation = () => {
  emit('location-update-requested', guideData.value.id);
  console.log(`请求更新${guideData.value.name}的位置`);
};

// 报警类型选择器引用
const alarmTypeSelectorRef = ref(null);

// 手动报警器触发（原紧急呼叫功能）
const triggerAlarm = () => {
  // 显示报警类型选择弹窗
  alarmTypeSelectorRef.value?.show();
};

// 处理报警类型确认
const handleAlarmConfirm = (alarmType) => {
  const currentGuide = guideList.value.find(guide => guide.id === selectedGuideId.value) || guideData.value;

  // 发送报警触发事件给父组件
  emit('alarm-triggered', {
    alarmType: alarmType,
    reporter: currentGuide.name,
    guideId: currentGuide.id,
    location: currentGuide.location,
    timestamp: new Date().toLocaleString()
  });

  console.log(`手动报警器触发: ${alarmType} by ${currentGuide.name}`);
};

// 处理报警类型取消
const handleAlarmCancel = () => {
  console.log('报警触发已取消');
};

// 暴露事件给父组件
const emit = defineEmits([
  'guide-selected',
  'task-assigned',
  'voice-call-started',
  'guide-message-sent',
  'location-update-requested',
  'alarm-triggered'  // 修改：从emergency-call改为alarm-triggered
]);

// 可以通过props接收配置
const props = defineProps({
  guides: {
    type: Array,
    default: () => []
  },
  defaultGuideId: {
    type: String,
    default: 'G001'
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

// 如果有外部引导员数据，使用外部数据
if (props.guides && props.guides.length > 0) {
  guideList.value = props.guides;
}

if (props.defaultGuideId) {
  selectedGuideId.value = props.defaultGuideId;
  selectGuide(props.defaultGuideId);
}

// 暴露方法给父组件
defineExpose({
  selectGuide,
  assignTask,
  startVoiceCall,
  sendMessage,
  updateLocation,
  triggerAlarm,  // 修改：从emergencyCall改为triggerAlarm
  getCurrentGuide: () => guideData.value
});
</script>

<template>
  <div class="panel-section guide-management">
    <div class="guide-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      引导员管理
      <div class="guide-selector" v-if="guideList.length > 1">
        <select v-model="selectedGuideId" @change="selectGuide(selectedGuideId)">
          <option v-for="guide in guideList" :key="guide.id" :value="guide.id">
            {{ guide.name }} ({{ guide.location }})
          </option>
        </select>
      </div>
    </div>
    <div class="guide-content">
      <div style="display: flex; margin-bottom: 10px;">
        <div class="guide-display">
          <img :src="guideImage" alt="引导员" class="guide-image">
          <div class="status-badge" :style="{ backgroundColor: statusColor }">
            {{ statusText }}
          </div>
        </div>
        <div class="guide-details">
          <p><strong>姓名:</strong> {{ guideData.name }}</p>
          <p><strong>职位:</strong> {{ guideData.position }}</p>
          <p><strong>位置:</strong> {{ guideData.location }}</p>
          <p><strong>电话:</strong> {{ guideData.phone }}</p>
          <p v-if="guideData.currentTask"><strong>当前任务:</strong> {{ guideData.currentTask.description || guideData.currentTask.type }}</p>
        </div>
      </div>
      
      <div class="guide-info">
        <div class="guide-buttons" v-if="!props.readonly">
          <button 
            class="guide-btn" 
            @click="showAssignTask"
            :disabled="guideData.status === 'offline'"
          >
            派单
          </button>
          <button 
            class="guide-btn voice-btn" 
            @click="startVoiceCall"
            :disabled="guideData.status === 'offline'"
          >
            语音
          </button>
          <button class="guide-btn secondary" @click="updateLocation">
            定位
          </button>
          <button class="guide-btn emergency" @click="triggerAlarm">
            紧急
          </button>
        </div>
      </div>
      
      <div class="last-seen" v-if="guideData.lastSeen">
        最后在线: {{ guideData.lastSeen }}
      </div>
    </div>

    <!-- 报警类型选择弹窗 -->
    <AlarmTypeSelector
      ref="alarmTypeSelectorRef"
      @confirm="handleAlarmConfirm"
      @cancel="handleAlarmCancel"
    />

    <!-- 派单表单弹窗 -->
    <div v-if="showTaskForm" class="task-form-overlay">
      <div class="task-form">
        <div class="form-header">
          <h3>派发任务给 {{ guideData.name }}</h3>
          <button class="close-btn" @click="cancelAssignTask">×</button>
        </div>
        <div class="form-body">
          <div class="form-group">
            <label>任务类型:</label>
            <select v-model="taskForm.type">
              <option v-for="type in taskTypes" :key="type.value" :value="type.value">
                {{ type.label }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>任务描述:</label>
            <textarea v-model="taskForm.description" placeholder="请输入任务详细描述"></textarea>
          </div>
          <div class="form-group">
            <label>优先级:</label>
            <select v-model="taskForm.priority">
              <option value="low">低</option>
              <option value="normal">普通</option>
              <option value="high">高</option>
              <option value="urgent">紧急</option>
            </select>
          </div>
          <div class="form-group">
            <label>执行地点:</label>
            <input v-model="taskForm.location" placeholder="请输入执行地点" />
          </div>
          <div class="form-group">
            <label>截止时间:</label>
            <input type="datetime-local" v-model="taskForm.deadline" />
          </div>
        </div>
        <div class="form-footer">
          <button class="form-btn cancel" @click="cancelAssignTask">取消</button>
          <button class="form-btn confirm" @click="assignTask">确认派发</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.guide-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

.guide-selector select {
  background-color: #1e3a5f;
  color: white;
  border: 1px solid #2997e3;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 0.8rem;
}

/* 引导员管理样式 */
.guide-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.guide-display {
  width: 40%;
  height: 150px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.guide-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  width: auto;
  height: auto;
  border-radius: 4px;
}

.status-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
}

.guide-info {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.guide-details {
  margin-left: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.guide-details p {
  margin: 5px 0;
  font-size: 0.9rem;
  color: white;
}

.guide-buttons {
  display: flex;
  justify-content: space-around;
  gap: 8px;
}

.guide-btn {
  flex: 1;
  background-color: #64991e;
  color: white;
  border: none;
  padding: 5px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.guide-btn:hover:not(:disabled) {
  background-color: #7cb305;
}

.guide-btn:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.guide-btn.voice-btn {
  background-color: #1890ff;
}

.guide-btn.voice-btn:hover:not(:disabled) {
  background-color: #40a9ff;
}

.guide-btn.secondary {
  background-color: #8c8c8c;
}

.guide-btn.secondary:hover:not(:disabled) {
  background-color: #999;
}

.guide-btn.emergency {
  background-color: #ff4d4f;
}

.guide-btn.emergency:hover:not(:disabled) {
  background-color: #ff7875;
}

.last-seen {
  font-size: 0.7rem;
  color: #8c8c8c;
  text-align: center;
  margin-top: 10px;
}

/* 派单表单样式 */
.task-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.task-form {
  background-color: #0d2b55;
  border: 1px solid #1e3a5f;
  border-radius: 8px;
  width: 400px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #1e3a5f;
}

.form-header h3 {
  margin: 0;
  color: white;
  font-size: 1.1rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.form-body {
  padding: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: white;
  font-size: 0.9rem;
}

.form-group select,
.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px;
  background-color: #1e3a5f;
  color: white;
  border: 1px solid #2997e3;
  border-radius: 4px;
  font-size: 0.9rem;
  box-sizing: border-box;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px;
  border-top: 1px solid #1e3a5f;
}

.form-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-btn.cancel {
  background-color: #8c8c8c;
  color: white;
}

.form-btn.cancel:hover {
  background-color: #999;
}

.form-btn.confirm {
  background-color: #52c41a;
  color: white;
}

.form-btn.confirm:hover {
  background-color: #73d13d;
}
</style>
