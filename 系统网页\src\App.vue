<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import HeaderNavigation from './components/HeaderNavigation.vue';
import LeftPanel from './components/LeftPanel.vue';
import CenterPanel from './components/CenterPanel.vue';
import RightPanel from './components/RightPanel.vue';

// 系统状态数据
const systemStatus = ref({
  building: {
    status: '密度过高',
    statusType: 'red',
    description: '当前状态：正在疏散...'
  },
  currentFloor: 'B1',
  mapScale: 1
});

// 地图标题状态
const mapTitle = ref('');

// 指示牌数据（保留用于其他组件可能的引用）
const signList = ref([]);

// 引导员数据
const guideList = ref([
  {
    id: 'G001',
    name: '李师傅',
    position: '地铁保安',
    location: 'B1层A区',
    phone: '18569874125',
    status: 'online'
  },
  {
    id: 'G002',
    name: '王师傅',
    position: '安全员',
    location: 'B2层B区',
    phone: '13812345678',
    status: 'busy'
  },
  {
    id: 'G003',
    name: '张师傅',
    position: '疏散员',
    location: 'M层C区',
    phone: '15987654321',
    status: 'offline'
  }
]);

// 全局报警状态管理
const alarmState = ref({
  alert: false,
  alertType: '',
  reporter: '',
  lastTriggered: null,
  isPolling: false,
  pollInterval: null
});

// 状态上报数据 - 清空模拟数据，改为动态管理
const statusReports = ref([]);

// 组件引用
const leftPanelRef = ref(null);
const centerPanelRef = ref(null);
const rightPanelRef = ref(null);

// ===== 报警器API调用函数 =====
const ALARM_API_BASE = 'http://139.196.23.0:5005';

// 查询报警器状态
const checkAlarmStatus = async () => {
  try {
    const response = await fetch(`${ALARM_API_BASE}/status`);
    if (response.ok) {
      const data = await response.json();
      const wasAlert = alarmState.value.alert;
      alarmState.value.alert = data.alert;

      // 更新建筑状态显示
      updateBuildingStatus(data.alert);

      // 如果状态从报警变为正常，调整轮询频率
      if (wasAlert && !data.alert) {
        startPolling(30000); // 恢复30秒轮询
      }

      return data;
    }
  } catch (error) {
    console.error('查询报警器状态失败:', error);
  }
  return null;
};

// 触发报警器
const triggerAlarm = async (alarmType, reporter) => {
  try {
    const data = {
      message: `手动报警器触发: ${alarmType}`,
      recipients: ["2015559", "2515523"],
      alert: true,
      alertType: alarmType,
      reporter: reporter
    };

    const response = await fetch(ALARM_API_BASE, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      console.log('API调用成功');
    }
  } catch (error) {
    console.error('触发报警器失败:', error);
  }

  // 无论API是否成功，都更新本地状态（用于演示）
  // 在生产环境中，这应该只在API成功时执行
  alarmState.value.alert = true;
  alarmState.value.alertType = alarmType;
  alarmState.value.reporter = reporter;
  alarmState.value.lastTriggered = new Date().toISOString();

  // 添加上报记录
  addStatusReport(alarmType, reporter);

  // 更新建筑状态
  updateBuildingStatus(true);

  // 开始高频轮询
  startPolling(5000);

  console.log(`本地状态更新成功: ${alarmType} by ${reporter}`);
  return true;
};

// 更新建筑状态显示
const updateBuildingStatus = (isAlert) => {
  if (isAlert) {
    systemStatus.value.building.status = '紧急状态';
    systemStatus.value.building.statusType = 'red';
    systemStatus.value.building.description = '手动报警器已触发';
  } else {
    systemStatus.value.building.status = '正常状态';
    systemStatus.value.building.statusType = 'green';
    systemStatus.value.building.description = '系统运行正常';
  }
};

// 添加状态上报记录
const addStatusReport = (content, reporter) => {
  const now = new Date();
  const timeStr = `${now.getFullYear()}/${String(now.getMonth() + 1).padStart(2, '0')}/${String(now.getDate()).padStart(2, '0')}/${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;

  const newReport = {
    time: timeStr,
    reporter: reporter,
    content: content
  };

  // 添加到列表开头
  statusReports.value.unshift(newReport);

  // 限制记录数量，保持最新的20条
  if (statusReports.value.length > 20) {
    statusReports.value = statusReports.value.slice(0, 20);
  }
};

// 开始轮询
const startPolling = (interval = 30000) => {
  // 清除现有轮询
  if (alarmState.value.pollInterval) {
    clearInterval(alarmState.value.pollInterval);
  }

  // 设置新的轮询
  alarmState.value.pollInterval = setInterval(checkAlarmStatus, interval);
  alarmState.value.isPolling = true;

  console.log(`开始轮询，间隔: ${interval}ms`);
};

// 停止轮询
const stopPolling = () => {
  if (alarmState.value.pollInterval) {
    clearInterval(alarmState.value.pollInterval);
    alarmState.value.pollInterval = null;
    alarmState.value.isPolling = false;
    console.log('停止轮询');
  }
};

// ===== 左侧面板事件处理 =====
const handleReportUpdated = (updateData) => {
  console.log('状态上报更新:', updateData);
  // 这里可以调用API更新后端数据
};

const handleSimulationStarted = () => {
  console.log('疏散仿真开始');
  systemStatus.value.building.description = '正在进行疏散仿真...';
  // 触发中间面板的疏散模拟
  centerPanelRef.value?.startEvacuationSimulation();
};

const handleSimulationCompleted = () => {
  console.log('疏散仿真完成');
  systemStatus.value.building.description = '疏散仿真已完成';
};

const handleOptimizationStarted = () => {
  console.log('路径优化开始');
  systemStatus.value.building.description = '正在进行路径优化...';
};

const handleOptimizationCompleted = () => {
  console.log('路径优化完成');
  systemStatus.value.building.description = '路径优化已完成';
};

const handleSignUpdateStarted = () => {
  console.log('指示牌更新开始');
  systemStatus.value.building.description = '正在更新指示牌...';
};

const handleSignUpdateCompleted = () => {
  console.log('指示牌更新完成');
  systemStatus.value.building.description = '指示牌更新已完成';
};

// ===== 中间面板事件处理 =====
const handleFloorChanged = (floor) => {
  systemStatus.value.currentFloor = floor;
  console.log(`楼层切换到: ${floor}`);
};

const handleZoomChanged = (scale) => {
  systemStatus.value.mapScale = scale;
  console.log(`地图缩放: ${scale}`);
};

const handleMapControlChanged = (controlData) => {
  console.log('地图控制变更:', controlData);
};

const handleEvacuationSimulationComplete = (data) => {
  console.log('疏散模拟完成:', data);
  systemStatus.value.building.description = '疏散模拟已完成';
};

const handleEvacuationDataUpdated = (data) => {
  console.log('疏散数据更新:', data.length, '个数据点');
};

const handleFlowDataUpdated = (data) => {
  console.log('客流数据更新:', data.length, '个数据点');
};

const handleFlowChartClicked = (params) => {
  console.log('客流图表点击:', params);
};

// ===== 右侧面板事件处理 =====
// 视频监控事件
const handleVideoConnected = (cameraId) => {
  console.log(`视频连接成功: ${cameraId}`);
};

const handleVideoDisconnected = () => {
  console.log('视频连接断开');
};

const handleVideoError = (error) => {
  console.error('视频错误:', error);
};

const handleCameraSwitched = (cameraId) => {
  console.log(`摄像头切换到: ${cameraId}`);
};

const handleQualityChanged = (quality) => {
  console.log(`视频质量调整为: ${quality}`);
};

const handleScreenshotTaken = (data) => {
  console.log('截图已保存:', data);
};

// 指示牌管理事件
const handleDeviceStatusChecked = (statusData) => {
  console.log('设备状态查询:', statusData);
};

const handleMessageSent = (messageData) => {
  console.log('消息发送:', messageData);
};

// 引导员管理事件
const handleGuideSelected = (guide) => {
  console.log('选中引导员:', guide);
};

const handleTaskAssigned = (task) => {
  console.log('任务派发:', task);
  // 更新引导员状态
  const guideIndex = guideList.value.findIndex(guide => guide.id === task.guideId);
  if (guideIndex !== -1) {
    guideList.value[guideIndex].status = 'busy';
  }
};

const handleVoiceCallStarted = (callData) => {
  console.log('语音通话开始:', callData);
};

const handleGuideMessageSent = (messageData) => {
  console.log('引导员消息发送:', messageData);
};

const handleLocationUpdateRequested = (guideId) => {
  console.log(`请求位置更新: ${guideId}`);
};

// 处理报警器触发（原紧急呼叫改为报警器触发）
const handleAlarmTriggered = async (alarmData) => {
  console.log('报警器触发:', alarmData);

  const { alarmType, reporter } = alarmData;
  const success = await triggerAlarm(alarmType, reporter);

  if (success) {
    console.log(`报警器触发成功: ${alarmType} by ${reporter}`);
  } else {
    console.error('报警器触发失败');
    // 可以在这里添加用户提示
  }
};

// 地图标题更新事件处理
const handleMapTitleUpdated = (title) => {
  mapTitle.value = title;
  console.log(`地图标题更新为: ${title}`);
};

// ===== 生命周期钩子 =====
onMounted(() => {
  // 组件挂载后立即查询一次状态
  checkAlarmStatus();
  // 开始正常轮询（30秒）
  startPolling(30000);
  console.log('报警器状态监控已启动');
});

onUnmounted(() => {
  // 组件卸载时停止轮询
  stopPolling();
  console.log('报警器状态监控已停止');
});
</script>

<template>
  <div class="evacuation-system">
    <!-- 顶部导航栏 -->
    <HeaderNavigation :map-title="mapTitle" />

    <!-- 主体内容区 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <LeftPanel
        ref="leftPanelRef"
        :building-status="systemStatus.building"
        :reports="statusReports"
        :alarm-state="alarmState"
        @report-updated="handleReportUpdated"
        @simulation-started="handleSimulationStarted"
        @simulation-completed="handleSimulationCompleted"
        @optimization-started="handleOptimizationStarted"
        @optimization-completed="handleOptimizationCompleted"
        @sign-update-started="handleSignUpdateStarted"
        @sign-update-completed="handleSignUpdateCompleted"
      />

      <!-- 中间区域 -->
      <CenterPanel
        ref="centerPanelRef"
        :initial-floor="systemStatus.currentFloor"
        :initial-scale="systemStatus.mapScale"
        :auto-start-flow-chart="true"
        :auto-start-evacuation="false"
        @floor-changed="handleFloorChanged"
        @zoom-changed="handleZoomChanged"
        @map-control-changed="handleMapControlChanged"
        @evacuation-simulation-complete="handleEvacuationSimulationComplete"
        @evacuation-data-updated="handleEvacuationDataUpdated"
        @flow-data-updated="handleFlowDataUpdated"
        @flow-chart-clicked="handleFlowChartClicked"
        @map-title-updated="handleMapTitleUpdated"
      />

      <!-- 右侧区域 -->
      <RightPanel
        ref="rightPanelRef"
        :guides="guideList"
        :device-i-p="'10.10.52.37'"
        :device-port="80"
        :username="'admin'"
        :password="'hucom12345'"
        :auto-connect-video="false"
        :show-video-controls="true"
        :default-camera="1"
        @video-connected="handleVideoConnected"
        @video-disconnected="handleVideoDisconnected"
        @video-error="handleVideoError"
        @camera-switched="handleCameraSwitched"
        @quality-changed="handleQualityChanged"
        @screenshot-taken="handleScreenshotTaken"
        @device-status-checked="handleDeviceStatusChecked"
        @message-sent="handleMessageSent"
        @guide-selected="handleGuideSelected"
        @task-assigned="handleTaskAssigned"
        @voice-call-started="handleVoiceCallStarted"
        @guide-message-sent="handleGuideMessageSent"
        @location-update-requested="handleLocationUpdateRequested"
        @alarm-triggered="handleAlarmTriggered"
      />
    </div>
  </div>
</template>

<style scoped>
/* 全局样式 */
.evacuation-system {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0d325f;
  color: white;
  font-family: Arial, sans-serif;
  overflow: visible;
  min-width: 1200px;
}

/* 主体内容区样式 */
.main-content {
  display: flex;
  flex: 1;
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .evacuation-system {
    min-width: 1000px;
  }
}

@media (max-width: 768px) {
  .evacuation-system {
    min-width: 100%;
    min-height: 100vh;
  }

  .main-content {
    flex-direction: column;
    overflow-y: visible;
  }
}

@media (max-width: 480px) {
  .evacuation-system {
    font-size: 0.9rem;
  }
}
</style>
