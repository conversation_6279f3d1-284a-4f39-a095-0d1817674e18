<script setup>
import { ref, computed } from 'vue';

// 状态上报数据 - 清空模拟数据，改为动态管理
const statusReports = ref([]);

// 接收props
const props = defineProps({
  reports: {
    type: Array,
    default: () => []
  }
});

// 使用外部数据（从App.vue传递的动态数据）
if (props.reports && props.reports.length > 0) {
  statusReports.value = props.reports;
}

// 计算属性：确保表格有最小高度，即使没有数据
const displayReports = computed(() => {
  // 如果有数据，直接返回
  if (props.reports && props.reports.length > 0) {
    return props.reports;
  }
  // 如果没有数据，返回空数组但保持表格结构
  return [];
});

// 计算表格最小高度（保持空间布局）
const tableMinHeight = computed(() => {
  // 至少显示7行的高度，保持原有布局
  return '280px';
});

// 暴露事件给父组件（保留接口，虽然现在不需要编辑）
const emit = defineEmits(['update-report']);
</script>

<template>
  <div class="panel-section status-report">
    <div class="status-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      实时状态上报
    </div>
    <div class="report-table">
      <div class="table-header">
        <div class="header-cell">告警时间</div>
        <div class="header-cell">上报人员</div>
        <div class="header-cell">上报内容</div>
      </div>
      <div class="table-body" :style="{ minHeight: tableMinHeight }">
        <div v-if="displayReports.length === 0" class="empty-state">
          <div class="empty-message">暂无报警记录</div>
        </div>
        <div v-for="(report, index) in displayReports" :key="index" class="table-row">
          <div class="cell">{{ report.time }}</div>
          <div class="cell">{{ report.reporter }}</div>
          <div class="cell">{{ report.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.status-title {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

/* 实时状态上报表格样式 */
.report-table {
  width: 100%;
}

.table-header {
  display: flex;
  background-color: rgba(255, 255, 255, 0.1);
  font-weight: bold;
}

.header-cell {
  flex: 1;
  padding: 3px;
  text-align: center;
  font-size: 0.8rem;
  color: #1e90ff;
}

.header-cell:last-child {
  border-right: none;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
  position: relative;
}

.table-row {
  display: flex;
}

.cell {
  flex: 1;
  padding: 6px;
  text-align: center;
  font-size: 0.8rem;
  color: white;
}

.cell:last-child {
  border-right: none;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
}

.empty-message {
  color: #8c8c8c;
  font-size: 0.9rem;
  font-style: italic;
}
</style>
